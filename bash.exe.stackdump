Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FE8E
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210286019, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E80  000210068E24 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9160  00021006A225 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD84A40000 ntdll.dll
7FFD83950000 KERNEL32.DLL
7FFD81B90000 KERNELBASE.dll
7FFD83100000 USER32.dll
7FFD82660000 win32u.dll
7FFD83340000 GDI32.dll
7FFD823D0000 gdi32full.dll
7FFD82100000 msvcp_win.dll
7FFD82510000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD827E0000 advapi32.dll
7FFD84560000 msvcrt.dll
7FFD834E0000 sechost.dll
7FFD82E50000 RPCRT4.dll
7FFD81170000 CRYPTBASE.DLL
7FFD82330000 bcryptPrimitives.dll
7FFD84520000 IMM32.DLL
